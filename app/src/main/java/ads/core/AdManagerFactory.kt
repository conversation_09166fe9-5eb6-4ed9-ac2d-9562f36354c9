package ads.core

import android.app.Application
import android.content.Context
import android.util.Log
import ads.admob.AppOpenAdManager
import ads.admob.InterstitialAdManager
import ads.admob.NativeAdManager
import ads.tradplus.TradPlusAppOpenAdManager
import ads.tradplus.TradPlusInterstitialAdManager
import ads.tradplus.TradPlusNativeAdManager

/**
 * 广告管理器工厂类
 * 根据配置动态创建对应的广告管理器实例
 */
class AdManagerFactory private constructor() {
    
    companion object {
        private const val TAG = "AdManagerFactory"
        
        @Volatile
        private var INSTANCE: AdManagerFactory? = null
        
        fun getInstance(): AdManagerFactory {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AdManagerFactory().also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 创建插屏广告管理器
     */
    fun createInterstitialAdManager(context: Context, platform: AdPlatform? = null): InterstitialAdManagerInterface {
        val targetPlatform = platform ?: AdConfigManager.getInstance(context).getCurrentPlatform()
        
        return when (targetPlatform) {
            AdPlatform.ADMOB -> {
                Log.d(TAG, "Creating AdMob InterstitialAdManager")
                AdMobInterstitialAdManagerAdapter(context)
            }
            AdPlatform.TRADPLUS -> {
                Log.d(TAG, "Creating TradPlus InterstitialAdManager")
                TradPlusInterstitialAdManagerAdapter(context)
            }
        }
    }
    
    /**
     * 创建原生广告管理器
     */
    fun createNativeAdManager(context: Context, platform: AdPlatform? = null): NativeAdManagerInterface {
        val targetPlatform = platform ?: AdConfigManager.getInstance(context).getCurrentPlatform()
        
        return when (targetPlatform) {
            AdPlatform.ADMOB -> {
                Log.d(TAG, "Creating AdMob NativeAdManager")
                AdMobNativeAdManagerAdapter(context)
            }
            AdPlatform.TRADPLUS -> {
                Log.d(TAG, "Creating TradPlus NativeAdManager")
                TradPlusNativeAdManagerAdapter(context)
            }
        }
    }
    
    /**
     * 创建开屏广告管理器
     */
    fun createAppOpenAdManager(application: Application, platform: AdPlatform? = null): AppOpenAdManagerInterface {
        val targetPlatform = platform ?: AdConfigManager.getInstance(application).getCurrentPlatform()
        
        return when (targetPlatform) {
            AdPlatform.ADMOB -> {
                Log.d(TAG, "Creating AdMob AppOpenAdManager")
                AdMobAppOpenAdManagerAdapter(application)
            }
            AdPlatform.TRADPLUS -> {
                Log.d(TAG, "Creating TradPlus AppOpenAdManager")
                TradPlusAppOpenAdManagerAdapter(application)
            }
        }
    }
}

/**
 * AdMob 插屏广告管理器适配器
 * 委托给独立的 InterstitialAdManager 类
 */
private class AdMobInterstitialAdManagerAdapter(context: Context) : InterstitialAdManagerInterface {
    private val adManager = InterstitialAdManager(context)

    override fun loadAd(listener: AdLoadListener?) {
        // 先加载广告
        adManager.loadAd()

        // 模拟加载过程，检查是否加载成功
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            if (adManager.isAdAvailable()) {
                listener?.onAdLoaded()
            } else {
                listener?.onAdFailedToLoad("AdMob 插屏广告加载失败")
            }
        }, 1000) // 延迟1秒检查
    }

    override fun isAdAvailable(): Boolean = adManager.isAdAvailable()

    override fun destroy() {
        // InterstitialAdManager 没有 destroy 方法，无需处理
    }

    override fun showAd(activity: android.app.Activity, listener: AdShowListener?) {
        if (!isAdAvailable()) {
            listener?.onAdFailedToShow("AdMob 插屏广告未准备好")
            return
        }

        adManager.showAd(activity) {
            listener?.onAdDismissed()
        }
        listener?.onAdShowed()
    }
}

/**
 * TradPlus 插屏广告管理器适配器
 * 委托给独立的 TradPlusInterstitialAdManager 类
 */
private class TradPlusInterstitialAdManagerAdapter(context: Context) : InterstitialAdManagerInterface {
    private val adManager = TradPlusInterstitialAdManager(context)

    override fun loadAd(listener: AdLoadListener?) {
        // 先加载广告
        adManager.loadAd()

        // 模拟加载过程，检查是否加载成功
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            if (adManager.isAdAvailable()) {
                listener?.onAdLoaded()
            } else {
                listener?.onAdFailedToLoad("TradPlus 插屏广告加载失败")
            }
        }, 1200) // 延迟1.2秒检查
    }

    override fun isAdAvailable(): Boolean = adManager.isAdAvailable()

    override fun destroy() {
        adManager.onDestroy()
    }

    override fun showAd(activity: android.app.Activity, listener: AdShowListener?) {
        if (!isAdAvailable()) {
            listener?.onAdFailedToShow("TradPlus 插屏广告未准备好")
            return
        }

        adManager.showAd(activity) {
            listener?.onAdDismissed()
        }
        listener?.onAdShowed()
    }
}

/**
 * AdMob 原生广告管理器适配器
 */
private class AdMobNativeAdManagerAdapter(context: Context) : NativeAdManagerInterface {
    private val adManager = NativeAdManager(context)
    
    override fun loadAd(listener: AdLoadListener?) {
        // 这个方法在接口中定义但在原生广告中不常用，模拟加载过程
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            if (adManager.isAdAvailable()) {
                listener?.onAdLoaded()
            } else {
                listener?.onAdFailedToLoad("AdMob 原生广告加载失败")
            }
        }, 1200) // 延迟1.2秒
    }
    
    override fun loadAd(listener: NativeAdLoadListener?) {
        adManager.loadAd(object : NativeAdManager.NativeAdLoadListener {
            override fun onAdLoaded(adView: com.google.android.gms.ads.nativead.NativeAdView) {
                listener?.onAdLoaded(adView)
            }
            
            override fun onAdFailedToLoad(error: String) {
                listener?.onAdFailedToLoad(error)
            }
        })
    }
    
    override fun isAdAvailable(): Boolean = adManager.isAdAvailable()
    
    override fun destroy() {
        adManager.destroyNativeAd()
    }
}

/**
 * TradPlus 原生广告管理器适配器
 */
private class TradPlusNativeAdManagerAdapter(context: Context) : NativeAdManagerInterface {
    private val adManager = TradPlusNativeAdManager(context)
    
    override fun loadAd(listener: AdLoadListener?) {
        // 这个方法在接口中定义但在原生广告中不常用
        // 直接调用真实的加载方法
        loadAd(object : NativeAdLoadListener {
            override fun onAdLoaded(adView: android.view.ViewGroup) {
                listener?.onAdLoaded()
            }

            override fun onAdFailedToLoad(error: String) {
                listener?.onAdFailedToLoad(error)
            }
        })
    }
    
    override fun loadAd(listener: NativeAdLoadListener?) {
        adManager.loadAd(object : TradPlusNativeAdManager.NativeAdLoadListener {
            override fun onAdLoaded(adView: android.view.ViewGroup) {
                listener?.onAdLoaded(adView)
            }
            
            override fun onAdFailedToLoad(error: String) {
                listener?.onAdFailedToLoad(error)
            }
        })
    }
    
    override fun isAdAvailable(): Boolean = adManager.isAdAvailable()
    
    override fun destroy() {
        adManager.destroyNativeAd()
    }
}

/**
 * AdMob 开屏广告管理器适配器
 * 委托给独立的 AppOpenAdManager 类
 */
private class AdMobAppOpenAdManagerAdapter(application: Application) : AppOpenAdManagerInterface {
    private val adManager = AppOpenAdManager(application)

    override fun loadAd(listener: AdLoadListener?) {
        // AppOpenAdManager 在初始化时自动加载广告
        // 模拟加载过程
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            listener?.onAdLoaded()
        }, 1000) // 延迟1秒
    }

    override fun isAdAvailable(): Boolean {
        // AppOpenAdManager 没有公开的 isAdAvailable 方法
        // 这里返回 true，实际可用性在 showAdIfAvailable 中处理
        return true
    }

    override fun destroy() {
        // AppOpenAdManager 没有 destroy 方法，无需处理
    }

    override fun showAdIfAvailable(activity: android.app.Activity?, listener: AdShowListener?) {
        if (activity != null) {
            // 调用新的带 Activity 参数的方法
            adManager.showAdIfAvailable(activity)

            // 模拟广告显示回调（因为原始 AppOpenAdManager 没有回调机制）
            listener?.onAdShowed()

            // 延迟模拟广告关闭
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                listener?.onAdDismissed()
            }, 3000) // 3秒后模拟关闭
        } else {
            listener?.onAdFailedToShow("AdMob 开屏广告需要 Activity 上下文")
        }
    }

    override fun onAppForegrounded() {
        adManager.onAppForegrounded()
    }
}

/**
 * TradPlus 开屏广告管理器适配器
 * 使用现有的 TradPlusAppOpenAdManager 而不是重复实现
 */
private class TradPlusAppOpenAdManagerAdapter(application: Application) : AppOpenAdManagerInterface {
    private val adManager = TradPlusAppOpenAdManager(application)

    override fun loadAd(listener: AdLoadListener?) {
        adManager.loadAd(
            onSuccess = { listener?.onAdLoaded() },
            onFailure = { error -> listener?.onAdFailedToLoad(error) }
        )
    }

    override fun isAdAvailable(): Boolean {
        return adManager.isAdAvailable()
    }

    override fun destroy() {
        adManager.onDestroy()
    }

    override fun showAdIfAvailable(activity: android.app.Activity?, listener: AdShowListener?) {
        adManager.showAdIfAvailable(
            activity = activity,
            onShowed = { listener?.onAdShowed() },
            onDismissed = { listener?.onAdDismissed() },
            onFailed = { error -> listener?.onAdFailedToShow(error) },
            onClicked = { listener?.onAdClicked() }
        )
    }

    override fun onAppForegrounded() {
        adManager.onAppForegrounded()
    }
}
