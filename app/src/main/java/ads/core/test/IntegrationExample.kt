package ads.core.test

import ads.core.UnifiedAdManager
import android.content.Context
import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp

/**
 * 集成示例
 * 展示如何在现有应用中集成广告测试功能
 */

/**
 * 示例1: 在设置页面中添加广告测试入口
 */
@Composable
fun SettingsPageWithAdTest() {
    val context = LocalContext.current
    
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            Text(
                text = "设置",
                style = MaterialTheme.typography.headlineMedium
            )
        }
        
        item {
            SettingsItem(
                title = "语言设置",
                description = "选择应用语言",
                onClick = { /* 语言设置逻辑 */ }
            )
        }
        
        item {
            SettingsItem(
                title = "主题设置",
                description = "选择应用主题",
                onClick = { /* 主题设置逻辑 */ }
            )
        }
        
        item {
            SettingsItem(
                title = "隐私设置",
                description = "管理隐私选项",
                onClick = { /* 隐私设置逻辑 */ }
            )
        }
        
        // 在Debug模式下显示广告测试入口
        if (BuildConfig.DEBUG) {
            item {
                Divider(modifier = Modifier.padding(vertical = 8.dp))
            }
            
            item {
                Text(
                    text = "开发者选项",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            item {
                AdTestEntry(
                    modifier = Modifier.fillMaxWidth(),
                    showAsFloatingButton = false
                )
            }
        }
    }
}

/**
 * 示例2: 在主页面中添加隐藏的测试入口
 */
@Composable
fun MainPageWithHiddenAdTest() {
    Box(modifier = Modifier.fillMaxSize()) {
        // 主要内容
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "欢迎使用应用",
                style = MaterialTheme.typography.headlineLarge
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            Button(
                onClick = { /* 主要功能 */ }
            ) {
                Text("开始使用")
            }
        }
        
        // 隐藏的测试入口（右下角）
        if (BuildConfig.DEBUG) {
            HiddenAdTestEntry(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(16.dp),
                triggerText = "🔧"
            )
        }
    }
}

/**
 * 示例3: 在开发者菜单中添加广告测试选项
 */
@Composable
fun DeveloperMenu() {
    var showMenu by remember { mutableStateOf(false) }
    val context = LocalContext.current
    
    if (BuildConfig.DEBUG) {
        FloatingActionButton(
            onClick = { showMenu = true },
            modifier = Modifier.padding(16.dp)
        ) {
            Text("DEV")
        }
        
        if (showMenu) {
            AlertDialog(
                onDismissRequest = { showMenu = false },
                title = { Text("开发者菜单") },
                text = {
                    Column {
                        DeveloperMenuItem(
                            title = "🧪 广告测试UI",
                            onClick = {
                                AdTestLauncher.launchAdTestUI(context)
                                showMenu = false
                            }
                        )
                        
                        DeveloperMenuItem(
                            title = "🔄 快速切换测试",
                            onClick = {
                                AdTestLauncher.launchAdSwitchTest(context)
                                showMenu = false
                            }
                        )
                        
                        DeveloperMenuItem(
                            title = "⚡ 快速功能测试",
                            onClick = {
                                AdTestHelper.quickTest(context)
                                showMenu = false
                            }
                        )
                        
                        DeveloperMenuItem(
                            title = "📊 查看广告状态",
                            onClick = {
                                AdTestHelper.showCurrentStatus(context)
                                showMenu = false
                            }
                        )
                        
                        DeveloperMenuItem(
                            title = "🔧 完整测试套件",
                            onClick = {
                                AdTestHelper.runFullTest(context)
                                showMenu = false
                            }
                        )
                    }
                },
                confirmButton = {
                    TextButton(onClick = { showMenu = false }) {
                        Text("关闭")
                    }
                }
            )
        }
    }
}

@Composable
private fun SettingsItem(
    title: String,
    description: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        onClick = onClick
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun DeveloperMenuItem(
    title: String,
    onClick: () -> Unit
) {
    TextButton(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = title,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * 示例4: 在应用启动时进行广告系统检查
 */
object AdSystemChecker {
    
    /**
     * 在应用启动时检查广告系统状态
     */
    fun checkAdSystemOnStartup(context: Context) {
        if (BuildConfig.DEBUG) {
            try {
                val unifiedAdManager = UnifiedAdManager.Companion.getInstance(context)
                val currentPlatform = unifiedAdManager.getCurrentPlatform()
                
                Log.d("AdSystemChecker", "广告系统启动检查:")
                Log.d("AdSystemChecker", "当前平台: $currentPlatform")
                
                // 可以在这里添加更多的启动检查逻辑
                
            } catch (e: Exception) {
                Log.e("AdSystemChecker", "广告系统检查失败", e)
            }
        }
    }
    
    /**
     * 检查广告管理器是否正常工作
     */
    fun checkAdManagers(context: Context): Boolean {
        return try {
            val unifiedAdManager = UnifiedAdManager.Companion.getInstance(context)
            
            // 尝试创建各种管理器
            val interstitialManager = unifiedAdManager.getInterstitialAdManager()
            val nativeManager = unifiedAdManager.getNativeAdManager()
            val appOpenManager = unifiedAdManager.getAppOpenAdManager()
            
            Log.d("AdSystemChecker", "所有广告管理器创建成功")
            true
        } catch (e: Exception) {
            Log.e("AdSystemChecker", "广告管理器检查失败", e)
            false
        }
    }
}

/**
 * BuildConfig 兼容性处理
 * 如果项目中没有 BuildConfig，可以使用这个替代方案
 */
object BuildConfig {
    // 在实际项目中，这个值应该从 build.gradle 中获取
    // 这里提供一个简单的实现
    val DEBUG: Boolean = true // 在发布版本中设置为 false
}
