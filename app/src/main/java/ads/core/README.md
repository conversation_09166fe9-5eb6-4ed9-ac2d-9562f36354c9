# 统一广告聚合系统

这是一个支持 AdMob 和 TradPlus 动态切换的统一广告管理系统。

## 🚀 主要特性

- **动态平台切换**: 支持运行时动态切换广告平台
- **远程配置支持**: 支持通过远程配置控制广告平台选择
- **统一接口**: 提供统一的广告管理接口，无需关心底层平台差异
- **本地配置持久化**: 自动保存配置到本地，应用重启后保持设置
- **向后兼容**: 兼容原有的布尔值配置接口

## 📁 架构设计

```
ads/core/
├── AdPlatform.kt              # 广告平台枚举
├── AdLoadListener.kt          # 广告加载监听器接口
├── BaseAdManager.kt           # 广告管理器基础接口
├── AdConfigManager.kt         # 配置管理器
├── AdManagerFactory.kt        # 广告管理器工厂
├── UnifiedAdManager.kt        # 统一广告管理器
├── AdSwitchTestActivity.kt    # 测试Activity
├── AdUsageExample.kt          # 使用示例
└── README.md                  # 本文档
```

## 🔧 快速开始

### 1. 基本使用

```kotlin
// 获取插屏广告管理器
val interstitialManager = AdManagerHelper.getInterstitialAdManager(context)

// 加载广告
interstitialManager.loadAd()

// 显示广告
if (interstitialManager.isAdAvailable()) {
    interstitialManager.showAd(activity) {
        // 广告关闭回调
    }
}
```

### 2. 切换广告平台

```kotlin
// 方式1: 直接切换
AdManagerHelper.switchAdPlatform(context, AdPlatform.TRADPLUS)

// 方式2: 布尔值接口（兼容原有代码）
AdManagerHelper.setUseTradPlus(context, true)
```

### 3. 远程配置更新

```kotlin
// 在 Application 中处理远程配置
val remoteConfig = mapOf(
    "ad_platform" to "TRADPLUS"  // 或 "ADMOB"
)

val unifiedManager = UnifiedAdManager.getInstance(this)
unifiedManager.updateFromRemoteConfig(remoteConfig)
```

## 📋 配置说明

### 远程配置格式

支持两种配置格式：

**新格式（推荐）:**
```json
{
    "ad_platform": "TRADPLUS"  // 或 "ADMOB"
}
```

**旧格式（兼容性）:**
```json
{
    "use_tradplus": true  // true 使用 TradPlus，false 使用 AdMob
}
```

### 本地配置

配置会自动保存到 SharedPreferences 中：
- 键名: `ad_platform`
- 默认值: `ADMOB`

## 🧪 测试

使用 `AdSwitchTestActivity` 进行功能测试：

1. 添加到 AndroidManifest.xml:

```xml

<activity
        android:name="ads.core.test.AdSwitchTestActivity"
        android:exported="true"/>
```

2. 启动测试Activity:
```kotlin
startActivity(Intent(this, AdSwitchTestActivity::class.java))
```

## 📖 详细使用指南

### 插屏广告

```kotlin
val interstitialManager = AdManagerHelper.getInterstitialAdManager(context)

// 加载广告
interstitialManager.loadAd(object : AdLoadListener {
    override fun onAdLoaded() {
        // 广告加载成功
    }
    
    override fun onAdFailedToLoad(error: String) {
        // 广告加载失败
    }
})

// 显示广告
interstitialManager.showAd(activity, object : AdShowListener {
    override fun onAdShowed() { /* 广告显示 */ }
    override fun onAdDismissed() { /* 广告关闭 */ }
    override fun onAdFailedToShow(error: String) { /* 显示失败 */ }
    override fun onAdClicked() { /* 广告点击 */ }
})
```

### 原生广告

```kotlin
val nativeManager = AdManagerHelper.getNativeAdManager(context)

nativeManager.loadAd(object : NativeAdLoadListener {
    override fun onAdLoaded(adView: ViewGroup) {
        // 将 adView 添加到您的布局中
        container.addView(adView)
    }
    
    override fun onAdFailedToLoad(error: String) {
        // 处理加载失败
    }
})
```

### 开屏广告

开屏广告会在 Application 中自动管理，无需手动调用。

## ⚠️ 注意事项

1. **初始化顺序**: 系统会根据配置自动初始化对应的广告平台，无需手动初始化
2. **资源管理**: UnifiedAdManager 是单例，不要手动调用 destroy()
3. **线程安全**: 所有接口都是线程安全的
4. **配置变更**: 平台切换会自动销毁旧的广告实例并创建新的实例

## 🔄 迁移指南

### 从原有代码迁移

**原有代码:**
```kotlin
val interstitialManager = InterstitialAdManager(context)
interstitialManager.loadAd()
interstitialManager.showAd(activity) { }
```

**新代码:**
```kotlin
val interstitialManager = AdManagerHelper.getInterstitialAdManager(context)
interstitialManager.loadAd()
interstitialManager.showAd(activity) { }
```

### 配置迁移

原有的布尔值配置会自动兼容，无需修改。

## 🐛 故障排除

### 常见问题

1. **广告加载失败**: 检查网络连接和广告位ID配置
2. **平台切换无效**: 确保调用了正确的切换方法
3. **配置不生效**: 检查远程配置格式是否正确

### 调试日志

启用详细日志查看系统运行状态：
```kotlin
// 查看当前平台
Log.d("AdDebug", "Current platform: ${unifiedManager.getCurrentPlatform()}")
```

## 📞 技术支持

如有问题，请查看：
1. 示例代码: `AdUsageExample.kt`
2. 测试Activity: `AdSwitchTestActivity.kt`
3. 日志输出: 搜索 "UnifiedAdManager" 标签
