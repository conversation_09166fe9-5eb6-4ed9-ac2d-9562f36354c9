package com.example.recoverdev.data

import java.io.File
import java.util.Date

/**
 * 表示一个可恢复的文件
 */
data class RecoveredFile(
    val originalPath: String,
    val fileName: String,
    val fileSize: Long,
    val fileType: FileType,
    val deletedDate: Date?,
    val recoveryConfidence: Float, // 0.0 - 1.0 恢复成功率
    val thumbnailPath: String? = null,
    val isRecoverable: Boolean = true
)

enum class FileType(val extension: List<String>, val displayName: String) {
    IMAGE(listOf("jpg", "jpeg", "png", "gif", "bmp", "webp"), "图片"),
    VIDEO(listOf("mp4", "avi", "mkv", "mov", "3gp", "wmv"), "视频"),
    AUDIO(listOf("mp3", "wav", "aac", "flac", "ogg", "m4a"), "音频"),
    DOCUMENT(listOf("pdf", "doc", "docx", "txt", "rtf"), "文档"),
    OTHER(emptyList(), "其他");
    
    companion object {
        fun fromExtension(extension: String): FileType {
            val ext = extension.lowercase()
            return values().find { it.extension.contains(ext) } ?: OTHER
        }
    }
}