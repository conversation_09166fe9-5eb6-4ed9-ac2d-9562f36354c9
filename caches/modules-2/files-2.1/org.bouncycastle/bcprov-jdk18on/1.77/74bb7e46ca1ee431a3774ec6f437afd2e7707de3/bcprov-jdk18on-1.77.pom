<?xml version="1.0" encoding="UTF-8"?>
<project>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.bouncycastle</groupId>
  <artifactId>bcprov-jdk18on</artifactId>
  <packaging>jar</packaging>
  <name>Bouncy Castle Provider</name>
  <version>1.77</version>
  <description>The Bouncy Castle Crypto package is a Java implementation of cryptographic algorithms. This jar contains JCE provider and lightweight API for the Bouncy Castle Cryptography APIs for JDK 1.8 and up.</description>
  <url>https://www.bouncycastle.org/java.html</url>
  <licenses>
    <license>
      <name>Bouncy Castle Licence</name>
      <url>https://www.bouncycastle.org/licence.html</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <url>https://github.com/bcgit/bc-java</url>
  </scm>
  <issueManagement>
     <system>GitHub</system>
     <url>https://github.com/bcgit/bc-java/issues</url>
  </issueManagement>
  <developers>
    <developer>
      <id>feedback-crypto</id>
      <name>The Legion of the Bouncy Castle Inc.</name>
      <email><EMAIL></email>
    </developer>
  </developers>
</project>
