{"formatVersion": "1.1", "component": {"group": "androidx.activity", "module": "activity-compose", "version": "1.9.2", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.7-rc-1"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.activity", "module": "activity-ktx", "version": {"requires": "1.9.2"}}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.0.1"}}, {"group": "androidx.compose.runtime", "module": "runtime-saveable", "version": {"requires": "1.0.1"}}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.0.1"}}, {"group": "androidx.lifecycle", "module": "lifecycle-viewmodel", "version": {"requires": "2.6.1"}}], "dependencyConstraints": [{"group": "androidx.activity", "module": "activity", "version": {"requires": "1.9.2"}, "reason": "activity-compose is in atomic group androidx.activity"}, {"group": "androidx.activity", "module": "activity-ktx", "version": {"requires": "1.9.2"}, "reason": "activity-compose is in atomic group androidx.activity"}], "files": [{"name": "activity-compose-1.9.2.aar", "url": "activity-compose-1.9.2.aar", "size": 1139308, "sha512": "50322010672de33ec0a4eecc6fde1c1951e057a2f72fd126da77240d24c7686109cd0f35a49c76403f27a87daaaf2ac058e2ff01db75f0ab0aa14a80af09e91d", "sha256": "adf2d7965116ea0d57b48e07c1e6e379739d0131084ec06c42bf9eabb963fbe6", "sha1": "01a20fe03dac05fdd7e936cf789a0888afe760b6", "md5": "c5075779552a194d1855f550bc57e4d9"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.activity", "module": "activity-ktx", "version": {"requires": "1.9.2"}}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.0.1"}}, {"group": "androidx.compose.runtime", "module": "runtime-saveable", "version": {"requires": "1.0.1"}}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.0.1"}}, {"group": "androidx.lifecycle", "module": "lifecycle-viewmodel", "version": {"requires": "2.6.1"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib", "version": {"requires": "1.8.22"}}], "dependencyConstraints": [{"group": "androidx.activity", "module": "activity", "version": {"requires": "1.9.2"}, "reason": "activity-compose is in atomic group androidx.activity"}, {"group": "androidx.activity", "module": "activity-ktx", "version": {"requires": "1.9.2"}, "reason": "activity-compose is in atomic group androidx.activity"}], "files": [{"name": "activity-compose-1.9.2.aar", "url": "activity-compose-1.9.2.aar", "size": 1139308, "sha512": "50322010672de33ec0a4eecc6fde1c1951e057a2f72fd126da77240d24c7686109cd0f35a49c76403f27a87daaaf2ac058e2ff01db75f0ab0aa14a80af09e91d", "sha256": "adf2d7965116ea0d57b48e07c1e6e379739d0131084ec06c42bf9eabb963fbe6", "sha1": "01a20fe03dac05fdd7e936cf789a0888afe760b6", "md5": "c5075779552a194d1855f550bc57e4d9"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "dependencyConstraints": [{"group": "androidx.activity", "module": "activity", "version": {"requires": "1.9.2"}, "reason": "activity-compose is in atomic group androidx.activity"}, {"group": "androidx.activity", "module": "activity-ktx", "version": {"requires": "1.9.2"}, "reason": "activity-compose is in atomic group androidx.activity"}], "files": [{"name": "activity-compose-1.9.2-sources.jar", "url": "activity-compose-1.9.2-sources.jar", "size": 10798, "sha512": "f6d37479072e7b36ccd67fbbf6ddd1a5152cdd55f66a528c45383ef23054444a5d3e4e709715235b1cad790471f93b6bc610575fb60ac909ca9406ad3a6c21a8", "sha256": "cb755641450ca9a588b77c52a9ee7495d85320ef14eeb3857163346f69b3e5c8", "sha1": "2b075be523b21154cb0b53fa22aa71f8c9ccddd1", "md5": "3c793ade40e5ae8dcc1fbdde4108c2a4"}, {"name": "activity-compose-1.9.2-samples-sources.jar", "url": "activity-compose-1.9.2-samples-sources.jar", "size": 3667, "sha512": "1140cf2b550b42ce26c25e6eeedbfe42c7118a3ac3610b64e838ae32f95850de5181e476bdb9826a9709498a94413dd8f1876bd00353dff39b784480cb3f6fb8", "sha256": "7d865092563b2fb1c0f0714f054f97501b8d0d607d07f5fee415baa994714143", "sha1": "d164345e6fcf25df9c64c75caadc53b32c9e69e2", "md5": "cb8ccda1fde3e809f793104f507e3cc7"}]}, {"name": "libraryVersionMetadata", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.usage": "library-version-metadata"}, "dependencyConstraints": [{"group": "androidx.activity", "module": "activity", "version": {"requires": "1.9.2"}, "reason": "activity-compose is in atomic group androidx.activity"}, {"group": "androidx.activity", "module": "activity-ktx", "version": {"requires": "1.9.2"}, "reason": "activity-compose is in atomic group androidx.activity"}], "files": [{"name": "activity-compose-1.9.2-versionMetadata.json", "url": "activity-compose-1.9.2-versionMetadata.json", "size": 2701, "sha512": "38e96fef550bc65a3cf2ef95fe4db98d47cef44c3db84ce19558c46543fce2b7f45b4dcad6c014993537706d3efd7d5d0b5b252b46c69d16a1e4b56fab8dee3f", "sha256": "edf2427da6f69559a4988536153f0826f46d319a7b2b80ef23c57fa83d23c7f3", "sha1": "3ce35c3833c670be76e757d0e68e8a9b4d0f6f15", "md5": "0866fd37c1abd4c3104bbf8b74e478d9"}]}]}