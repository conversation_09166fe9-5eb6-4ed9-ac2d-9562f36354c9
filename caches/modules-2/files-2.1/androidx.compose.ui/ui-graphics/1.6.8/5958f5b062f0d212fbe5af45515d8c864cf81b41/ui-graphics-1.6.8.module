{"formatVersion": "1.1", "component": {"group": "androidx.compose.ui", "module": "ui-graphics", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "androidxSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "androidx-multiplatform-docs"}, "dependencyConstraints": [{"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-geometry", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-junit4", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-manifest", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text-google-fonts", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-data", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-preview", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-unit", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-viewbinding", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}], "files": [{"name": "ui-graphics-1.6.8-multiplatform-sources.jar", "url": "ui-graphics-1.6.8-multiplatform-sources.jar", "size": 212266, "sha512": "f8c27e4864b4eeb226cb158c9680e5179cbce37f34dfb81f6689fec98b6a8c9fa0247adb5fca4176bf1d6826313bca5859088f4795fecc6ec0cd89ae396aa182", "sha256": "293d8cc26719df00ee86e13ec7dc6a0a48cb3ddb7350b874ff65131f6f13c697", "sha1": "172f5aa98c1f33fcf294c484d76fb6975efe064c", "md5": "a49cc74d2d04e998f1aa04a5ed93230c"}]}, {"name": "libraryVersionMetadata", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.usage": "library-version-metadata"}, "dependencyConstraints": [{"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-geometry", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-junit4", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-manifest", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text-google-fonts", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-data", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-preview", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-unit", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-viewbinding", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}], "files": [{"name": "apiLevels.json", "url": "ui-graphics-1.6.8-versionMetadata.json", "size": 75023, "sha512": "47abfd64c29840085de6f6bd1b612dfff2ff4ca4fe518ab66e5f2e6628eef1318cd5a952e2b88daad230a1ef968ac74a0dcfbcf6dd533a0f88039d49557dc6f9", "sha256": "9d4736d6e13d5fbf99c80415e9d7c092f44129ce824cffc563ca58fba011d3af", "sha1": "d459af89e90c148216db1167b6518c8bee001666", "md5": "49673e53cf576c77c1c8f0732c399f30"}]}, {"name": "metadataApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.platform.type": "common"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.7.0"}}, {"group": "androidx.compose.ui", "module": "ui-unit", "version": {"requires": "1.6.8"}}], "dependencyConstraints": [{"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-geometry", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-junit4", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-manifest", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text-google-fonts", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-data", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-preview", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-unit", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-viewbinding", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}], "files": [{"name": "ui-graphics-metadata-1.6.8.jar", "url": "ui-graphics-1.6.8.jar", "size": 731, "sha512": "6abaa4d1a964a7c17fd04d3d8260474ecb77157b31efd4802696bbfbaf2b31cab83847aee710aef57024cc74b1ac5159de7cd5f98b911b17c112b99a271f592c", "sha256": "eebcaad22243f49e712aae8ea011d72fb5caedd250e27e6cecd6a5b20fb61872", "sha1": "a63730bfb506f6b85f993e783c7feedd8b02a1c0", "md5": "2fd6e9c0f5248b4904051bee3f0327ad"}]}, {"name": "metadataSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.platform.type": "common"}, "dependencyConstraints": [{"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-geometry", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-junit4", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-manifest", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text-google-fonts", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-data", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-preview", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-unit", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-viewbinding", "version": {"requires": "1.6.8"}, "reason": "ui-graphics is in atomic group androidx.compose.ui"}], "files": [{"name": "ui-graphics-kotlin-1.6.8-sources.jar", "url": "ui-graphics-1.6.8-sources.jar", "size": 155487, "sha512": "41e966c33883f5430bb7d65bf82f57caaf5116176c8818b4aec425d1b09f54938eaab71cbd49d59e81395120779d13a909d0a97c3ec4defd6c44c290c95877d5", "sha256": "8bdc77c92c9e33d900394561aafbc30a6fc4974b8a9dfa1d1f1e99785b847925", "sha1": "e7b3ecc9d596387c238e09931d9a38ae404ec742", "md5": "3c021a12031042b53102077bee77fdf9"}]}, {"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../ui-graphics-android/1.6.8/ui-graphics-android-1.6.8.module", "group": "androidx.compose.ui", "module": "ui-graphics-android", "version": "1.6.8"}}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../ui-graphics-android/1.6.8/ui-graphics-android-1.6.8.module", "group": "androidx.compose.ui", "module": "ui-graphics-android", "version": "1.6.8"}}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../ui-graphics-android/1.6.8/ui-graphics-android-1.6.8.module", "group": "androidx.compose.ui", "module": "ui-graphics-android", "version": "1.6.8"}}, {"name": "desktopApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../ui-graphics-desktop/1.6.8/ui-graphics-desktop-1.6.8.module", "group": "androidx.compose.ui", "module": "ui-graphics-desktop", "version": "1.6.8"}}, {"name": "desktopRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../ui-graphics-desktop/1.6.8/ui-graphics-desktop-1.6.8.module", "group": "androidx.compose.ui", "module": "ui-graphics-desktop", "version": "1.6.8"}}, {"name": "desktopSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../ui-graphics-desktop/1.6.8/ui-graphics-desktop-1.6.8.module", "group": "androidx.compose.ui", "module": "ui-graphics-desktop", "version": "1.6.8"}}]}