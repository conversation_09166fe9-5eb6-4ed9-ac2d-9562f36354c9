{"formatVersion": "1.1", "component": {"url": "../../ui-unit/1.6.8/ui-unit-1.6.8.module", "group": "androidx.compose.ui", "module": "ui-unit", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}}, {"group": "androidx.compose.ui", "module": "ui-geometry", "version": {"requires": "1.6.8"}}], "dependencyConstraints": [{"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-geometry", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-graphics", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-junit4", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-manifest", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text-google-fonts", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-data", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-preview", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-viewbinding", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}], "files": [{"name": "ui-unit-release.aar", "url": "ui-unit-android-1.6.8.aar", "size": 84166, "sha512": "f89b4279e513200f64d4544a2a56c2b18a83ca7d7a319ccaaaccd242f4b0e96e180e0084a387a1239ee24ce59729436cfe6e3c5c42c1635f4a0751889630ce49", "sha256": "468ae1b39575bd2ebcdef9c89973fbaa56542d1a9b78fa9fe40f88e1369727a3", "sha1": "f0db04ece5465e7896040dc069d1ce2444684c12", "md5": "eb862ef2ac8c9d04c06eec814f4a6084"}]}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.collection", "module": "collection-ktx", "version": {"requires": "1.2.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-geometry", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}], "dependencyConstraints": [{"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-geometry", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-graphics", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-junit4", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-manifest", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text-google-fonts", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-data", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-preview", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-viewbinding", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}], "files": [{"name": "ui-unit-release.aar", "url": "ui-unit-android-1.6.8.aar", "size": 84166, "sha512": "f89b4279e513200f64d4544a2a56c2b18a83ca7d7a319ccaaaccd242f4b0e96e180e0084a387a1239ee24ce59729436cfe6e3c5c42c1635f4a0751889630ce49", "sha256": "468ae1b39575bd2ebcdef9c89973fbaa56542d1a9b78fa9fe40f88e1369727a3", "sha1": "f0db04ece5465e7896040dc069d1ce2444684c12", "md5": "eb862ef2ac8c9d04c06eec814f4a6084"}]}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencyConstraints": [{"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-geometry", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-graphics", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-junit4", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-test-manifest", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-text-google-fonts", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-data", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-tooling-preview", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}, {"group": "androidx.compose.ui", "module": "ui-viewbinding", "version": {"requires": "1.6.8"}, "reason": "ui-unit is in atomic group androidx.compose.ui"}], "files": [{"name": "ui-unit-android-1.6.8-sources.jar", "url": "ui-unit-android-1.6.8-sources.jar", "size": 37560, "sha512": "ef8856d06f7c50e172fb98cebcb77f85b5c22e0edb200ac286448b7aa83848853c05bbcb4b688dc27978db2c35c5d2665df5775616f795789e63980d73a78821", "sha256": "616f413fbeab02d1e10cfd26e4f6f7c5c6c58e16966d507d63972ebdd05b28d9", "sha1": "8fc47b5b72365cbd0aa504b06fd9d8e18ef535c5", "md5": "cf591f28dcf30085cb03e097016c7b5f"}]}]}