<?xml version="1.0" encoding="iso-8859-1"?>
<!--

    Copyright (c) 1997, 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->
<!--
    This project builds the JAF API jar file, which contains only
    the javax.activation.* API definitions and is *only* intended to be used
    for programs to compile against.  Note that it includes none of the
    implementation-specific classes that the javax.activation.* classes rely on.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.sun.activation</groupId>
        <artifactId>all</artifactId>
        <version>1.2.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>jakarta.activation</groupId>
    <artifactId>jakarta.activation-api</artifactId>
    <packaging>jar</packaging>
    <name>JavaBeans Activation Framework API jar</name>
    <properties>
        <activation.extensionName>
            jakarta.activation
        </activation.extensionName>
        <activation.moduleName>
            jakarta.activation
        </activation.moduleName>
        <activation.packages.export>
            javax.activation.*; version=${activation.spec.version}
        </activation.packages.export>
        <activation.bundle.symbolicName>
            jakarta.activation-api
        </activation.bundle.symbolicName>
    </properties>
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <!-- download the binaries -->
                        <id>get-binaries</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                    </execution>
                    <execution>
                        <!-- download the sources -->
                        <id>get-sources</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.sun.activation</groupId>
                                    <artifactId>jakarta.activation</artifactId>
                                    <version>${project.version}</version>
                                    <classifier>sources</classifier>
                                    <outputDirectory>
                                        ${project.build.directory}/sources
                                    </outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <artifactItems>
                        <artifactItem>
                            <groupId>com.sun.activation</groupId>
                            <artifactId>jakarta.activation</artifactId>
                            <version>${project.version}</version>
                        </artifactItem>
                    </artifactItems>
                    <outputDirectory>
                        ${project.build.outputDirectory}
                    </outputDirectory>
                    <includes>
                        javax/**,
                        META-INF/LICENSE.txt
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <finalName>${project.artifactId}</finalName>
                    <archive>
                        <manifestFile>
                            ${project.build.outputDirectory}/META-INF/MANIFEST.MF
                        </manifestFile>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
