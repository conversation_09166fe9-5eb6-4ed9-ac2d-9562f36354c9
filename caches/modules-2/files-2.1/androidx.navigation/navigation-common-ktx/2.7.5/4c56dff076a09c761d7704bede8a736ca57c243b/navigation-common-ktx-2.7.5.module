{"formatVersion": "1.1", "component": {"group": "androidx.navigation", "module": "navigation-common-ktx", "version": "2.7.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.0"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.navigation", "module": "navigation-common", "version": {"requires": "2.7.5"}}], "dependencyConstraints": [{"group": "androidx.navigation", "module": "navigation-common", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-compose", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-dynamic-features-fragment", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-dynamic-features-runtime", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-fragment", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-fragment-ktx", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-runtime", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-runtime-ktx", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-safe-args-generator", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-safe-args-gradle-plugin", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-testing", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-ui", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-ui-ktx", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}], "files": [{"name": "navigation-common-ktx-2.7.5.aar", "url": "navigation-common-ktx-2.7.5.aar", "size": 2116, "sha512": "25e0157c4c1beb25658fa2e68b301cee85d2e6927a41e277a4875020f25902f8075ab58c373f19ca291cb2eee78636c5fbc161b85277f77ed6668c862defb908", "sha256": "bd7ceba75d26b1f2ecfa931dae684678f07ca3ba1ed906a72c57b1a2d069daa4", "sha1": "a63e26a5ee52b555dcf1a55045afb4f2ef4dc243", "md5": "fc44e571d92c11f99f1be14f2c50c2e2"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.navigation", "module": "navigation-common", "version": {"requires": "2.7.5"}}], "dependencyConstraints": [{"group": "androidx.navigation", "module": "navigation-common", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-compose", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-dynamic-features-fragment", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-dynamic-features-runtime", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-fragment", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-fragment-ktx", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-runtime", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-runtime-ktx", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-safe-args-generator", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-safe-args-gradle-plugin", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-testing", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-ui", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-ui-ktx", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}], "files": [{"name": "navigation-common-ktx-2.7.5.aar", "url": "navigation-common-ktx-2.7.5.aar", "size": 2116, "sha512": "25e0157c4c1beb25658fa2e68b301cee85d2e6927a41e277a4875020f25902f8075ab58c373f19ca291cb2eee78636c5fbc161b85277f77ed6668c862defb908", "sha256": "bd7ceba75d26b1f2ecfa931dae684678f07ca3ba1ed906a72c57b1a2d069daa4", "sha1": "a63e26a5ee52b555dcf1a55045afb4f2ef4dc243", "md5": "fc44e571d92c11f99f1be14f2c50c2e2"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "dependencyConstraints": [{"group": "androidx.navigation", "module": "navigation-common", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-compose", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-dynamic-features-fragment", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-dynamic-features-runtime", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-fragment", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-fragment-ktx", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-runtime", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-runtime-ktx", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-safe-args-generator", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-safe-args-gradle-plugin", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-testing", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-ui", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-ui-ktx", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}], "files": [{"name": "navigation-common-ktx-2.7.5-sources.jar", "url": "navigation-common-ktx-2.7.5-sources.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}]}, {"name": "libraryVersionMetadata", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.usage": "library-version-metadata"}, "dependencyConstraints": [{"group": "androidx.navigation", "module": "navigation-common", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-compose", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-dynamic-features-fragment", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-dynamic-features-runtime", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-fragment", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-fragment-ktx", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-runtime", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-runtime-ktx", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-safe-args-generator", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-safe-args-gradle-plugin", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-testing", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-ui", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}, {"group": "androidx.navigation", "module": "navigation-ui-ktx", "version": {"requires": "2.7.5"}, "reason": "navigation-common-ktx is in atomic group androidx.navigation"}], "files": [{"name": "navigation-common-ktx-2.7.5-versionMetadata.json", "url": "navigation-common-ktx-2.7.5-versionMetadata.json", "size": 7299, "sha512": "0b14ddd94a9ecd51771f7260038d2f42b119c8f323493778ece2c0588187bba95b579c113c2221af74c61d5ac19dc0441126343526370b61659ad99b07fd93d8", "sha256": "aa7062c83cba708ab70e9c23522bf4627f435c6193af7699b4f07bd64b427109", "sha1": "793dcda8f3418446c939f7d04b1860dcb3ef2f74", "md5": "e929fe06c1b3502eaf88c5e8da543fd9"}]}]}