<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>androidx.navigation</groupId>
  <artifactId>navigation-common-ktx</artifactId>
  <version>2.7.5</version>
  <packaging>aar</packaging>
  <name>Navigation Common Kotlin Extensions</name>
  <description>Android Navigation-Common-Ktx</description>
  <url>https://developer.android.com/jetpack/androidx/releases/navigation#2.7.5</url>
  <inceptionYear>2018</inceptionYear>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Project</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://android.googlesource.com/platform/frameworks/support</connection>
    <url>https://cs.android.com/androidx/platform/frameworks/support</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-common</artifactId>
        <version>2.7.5</version>
      </dependency>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-compose</artifactId>
        <version>2.7.5</version>
      </dependency>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-dynamic-features-fragment</artifactId>
        <version>2.7.5</version>
      </dependency>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-dynamic-features-runtime</artifactId>
        <version>2.7.5</version>
      </dependency>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-fragment</artifactId>
        <version>2.7.5</version>
      </dependency>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-fragment-ktx</artifactId>
        <version>2.7.5</version>
      </dependency>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-runtime</artifactId>
        <version>2.7.5</version>
      </dependency>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-runtime-ktx</artifactId>
        <version>2.7.5</version>
      </dependency>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-safe-args-generator</artifactId>
        <version>2.7.5</version>
      </dependency>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-safe-args-gradle-plugin</artifactId>
        <version>2.7.5</version>
      </dependency>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-testing</artifactId>
        <version>2.7.5</version>
      </dependency>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-ui</artifactId>
        <version>2.7.5</version>
      </dependency>
      <dependency>
        <groupId>androidx.navigation</groupId>
        <artifactId>navigation-ui-ktx</artifactId>
        <version>2.7.5</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>androidx.navigation</groupId>
      <artifactId>navigation-common</artifactId>
      <version>[2.7.5]</version>
      <scope>compile</scope>
      <type>aar</type>
    </dependency>
  </dependencies>
</project>