{"formatVersion": "1.1", "component": {"group": "androidx.annotation", "module": "annotation", "version": "1.6.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.0-rc-2"}}, "variants": [{"name": "androidxSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "androidx-multiplatform-docs"}, "files": [{"name": "annotation-1.6.0-multiplatform-sources.jar", "url": "annotation-1.6.0-multiplatform-sources.jar", "size": 65101, "sha512": "9b276c19be093f0a84321ecc1de409a8c8d094d1ce87a7c96c387dbf67044c83cb20bb3950c2de1735b9d7acbc7153b52942c326729be2fffc683e7bac6e651c", "sha256": "151d35c34a17d6ac467a500e6d4c4761fa530791653399c1cbb68d2b193e1236", "sha1": "d9c403dc0dee1b743d3de3dd4c3c6f47167d9b95", "md5": "4929923ea2de529c6c584d3aa91ba65b"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "annotation-1.6.0-sources.jar", "url": "annotation-1.6.0-sources.jar", "size": 63544, "sha512": "82c39645dea79bab025d1044657018dae9f9869368ff010d69f861b05941fbd8fbbbd01d600dc76e2ddce20b7360955acaa7110b9e44dbde2abce8769f71c7fe", "sha256": "5e281c2fa5b123375a2b14a0756ee8c1bf3492eb882a6b1c3ba2775c4987283d", "sha1": "e86e963ceb76b44ab1ace2eed2aa5307c0e5db87", "md5": "4159bd381e1dba814fe5d1180def213e"}]}, {"name": "metadataApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.platform.type": "common"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib", "version": {"requires": "1.8.0"}}], "files": [{"name": "annotation-metadata-1.6.0.jar", "url": "annotation-1.6.0.jar", "size": 670, "sha512": "c3be9ce05a835030c5582975b5ab61f9b5af86bf9f2cf3c940d4746c9f58ac13d6086b2d3553aa41dd869008f846f40b6bb310e115cefbeb50aaf94ceb4f5fa7", "sha256": "fbc64f5c44a7added8b6eab517cf7d70555e25153bf5d44a6ed9b0e5312f7de9", "sha1": "b94fe605ea8ff0f68f3feab065ae0b7d67860964", "md5": "31d8d2ec69675d5797a0e290b0e3cd54"}]}, {"name": "jvmApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../annotation-jvm/1.6.0/annotation-jvm-1.6.0.module", "group": "androidx.annotation", "module": "annotation-jvm", "version": "1.6.0"}}, {"name": "jvmRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../annotation-jvm/1.6.0/annotation-jvm-1.6.0.module", "group": "androidx.annotation", "module": "annotation-jvm", "version": "1.6.0"}}]}