/*
 * Copyright 2020 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.internal.enterprise.impl.legacy;

import org.gradle.internal.scan.time.BuildScanClock;
import org.gradle.internal.time.Clock;

import javax.inject.Inject;

public class DefaultBuildScan<PERSON>lock implements BuildScan<PERSON>lock {

    private final Clock clock;

    @Inject
    public DefaultBuildScanClock(Clock clock) {
        this.clock = clock;
    }

    @Override
    public long getCurrentTime() {
        return clock.getCurrentTime();
    }

}
