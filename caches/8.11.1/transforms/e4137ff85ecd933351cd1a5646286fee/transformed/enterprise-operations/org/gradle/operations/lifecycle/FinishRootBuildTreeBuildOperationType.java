/*
 * Copyright 2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.operations.lifecycle;

import org.gradle.internal.operations.BuildOperationType;

/**
 * A build operation around the work that happens at the end of the root build.
 * <p>
 * {@code buildFinished} hooks and {@code FlowAction}s in the whole build tree run in this operation.
 * The start of the operation marks the end of the execution phase of the root build.
 *
 * @since 8.3
 */
public final class FinishRootBuildTreeBuildOperationType implements BuildOperationType<FinishRootBuildTreeBuildOperationType.Details, FinishRootBuildTreeBuildOperationType.Result> {

    public interface Details {
    }

    public interface Result {
    }
}
