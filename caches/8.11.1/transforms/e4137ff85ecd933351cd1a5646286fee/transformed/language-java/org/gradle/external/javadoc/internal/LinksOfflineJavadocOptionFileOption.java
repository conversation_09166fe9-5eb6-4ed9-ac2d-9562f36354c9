/*
 * Copyright 2009 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.external.javadoc.internal;

import com.google.common.collect.Lists;
import org.gradle.external.javadoc.JavadocOfflineLink;

import java.io.IOException;
import java.util.List;

public class LinksOfflineJavadocOptionFileOption extends AbstractJavadocOptionFileOption<List<JavadocOfflineLink>> {
    public LinksOfflineJavadocOptionFileOption(String option, List<JavadocOfflineLink> value) {
        super(option, value);
    }

    @Override
    public void write(JavadocOptionFileWriterContext writerContext) throws IOException {
        if (value != null && !value.isEmpty()) {
            for (final JavadocOfflineLink offlineLink : value) {
                writeOfflineLink(writerContext, offlineLink);
            }
        }
    }

    private void writeOfflineLink(JavadocOptionFileWriterContext writerContext, JavadocOfflineLink offlineLink) throws IOException {
        writerContext.writeOptionHeader(option);
        writerContext.writeValue(offlineLink.getExtDocUrl());
        writerContext.write(" ");
        writerContext.writeValue(offlineLink.getPackagelistLoc());
        writerContext.newLine();
    }

    @Override
    public LinksOfflineJavadocOptionFileOption duplicate() {
        List<JavadocOfflineLink> duplicateValue = Lists.newArrayList(value);
        return new LinksOfflineJavadocOptionFileOption(option, duplicateValue);
    }
}
