/*
 * Copyright 2007-2008 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.api.internal.artifacts.dependencies;

import org.gradle.api.artifacts.Dependency;

@SuppressWarnings("deprecation")
public abstract class AbstractDependency implements org.gradle.api.internal.artifacts.ResolvableDependency, Dependency {
    private String reason;

    protected void copyTo(AbstractDependency target) {
        target.reason = reason;
    }

    @Override
    @Deprecated
    public void resolve(org.gradle.api.internal.artifacts.CachingDependencyResolveContext context) {
    }

    @Override
    public String getReason() {
        return reason;
    }

    @Override
    public void because(String reason) {
        this.reason = reason;
    }
}
